// <PERSON><PERSON><PERSON> Console <PERSON> to Ingest Document for AI Chat
// Copy and paste this into your browser console while on your Overleaf project page

(async function ingestDocumentForAI() {
  console.log('🚀 Starting AI Document Ingestion...');
  
  // Get project ID from URL
  const projectId = window.location.pathname.match(/\/project\/([a-f0-9]{24})/)?.[1];
  if (!projectId) {
    console.error('❌ Could not find project ID in URL');
    return;
  }
  
  console.log('✅ Project ID:', projectId);
  
  // Sample LaTeX document - REPLACE THIS with your actual document content
  const documentContent = `
\\documentclass{article}
\\usepackage{amsmath}
\\usepackage{graphicx}
\\title{My Research Paper}
\\author{Your Name}
\\date{\\today}

\\begin{document}
\\maketitle

\\section{Introduction}
This document contains important research findings and mathematical formulations.
The main goal of this research is to explore advanced computational methods.

\\section{Methodology}
We used advanced techniques to analyze the data:
\\begin{itemize}
\\item Statistical analysis
\\item Machine learning algorithms
\\item Data visualization techniques
\\end{itemize}

\\section{Results}
The key equation we derived is:
\\begin{equation}
\\int_0^\\infty e^{-x^2} dx = \\frac{\\sqrt{\\pi}}{2}
\\end{equation}

This equation represents the Gaussian integral, which is fundamental in probability theory.

\\section{Discussion}
Our findings show significant improvements in the proposed method.
The results demonstrate a 25\\% increase in efficiency compared to previous approaches.

\\section{Conclusion}
This research contributes to the field by providing new insights into computational optimization.
Future work will focus on extending these methods to larger datasets.

\\bibliographystyle{plain}
\\bibliography{references}

\\end{document}
  `.trim();
  
  try {
    console.log('🔄 Sending document to AI ingestion endpoint...');
    
    // Get CSRF token from meta tag
    const csrfToken = document.querySelector('meta[name="ol-csrfToken"]')?.getAttribute('content');
    if (!csrfToken) {
      console.error('❌ Could not find CSRF token');
      return;
    }
    
    // Make the API request
    const response = await fetch(`/project/${projectId}/ai/ingest-document`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': csrfToken,
      },
      body: JSON.stringify({
        content: documentContent,
        documentType: 'latex'
      })
    });
    
    console.log('📡 Response status:', response.status);
    
    if (response.ok) {
      const result = await response.json();
      console.log('✅ Document ingestion successful!');
      console.log('📊 Result:', result);
      console.log(`🎉 Created ${result.chunksCount} chunks for AI processing`);
      
      console.log('');
      console.log('🎯 Next Steps:');
      console.log('1. Open the chat panel (if not already open)');
      console.log('2. Send AI queries like:');
      console.log('   • "@ai explain this document"');
      console.log('   • "@ai what is the main equation?"');
      console.log('   • "@ai summarize the methodology section"');
      console.log('   • "explain the Gaussian integral"');
      
    } else {
      const errorText = await response.text();
      console.error('❌ Document ingestion failed');
      console.error('Status:', response.status);
      console.error('Response:', errorText);
      
      if (response.status === 403) {
        console.log('💡 This might be a CSRF token issue. Try refreshing the page and running the script again.');
      }
    }
    
  } catch (error) {
    console.error('❌ Error during document ingestion:', error);
  }
})();

// Instructions for use:
console.log(`
🔧 HOW TO USE THIS SCRIPT:

1. Open your Overleaf project: http://localhost/project/6885bb82d8b9e3e61aa76d5d
2. Open browser developer tools (F12)
3. Go to the Console tab
4. IMPORTANT: Replace the documentContent variable above with your actual LaTeX document content
5. Copy and paste this entire script into the console
6. Press Enter to run it
7. Check the console output for success/error messages
8. If successful, try sending AI queries in the chat like "@ai explain this document"

📝 To customize the document content:
- Edit the documentContent variable in the script above
- Replace it with your actual LaTeX document content
- Make sure to escape any backslashes properly (use \\\\ instead of \\)
`);
