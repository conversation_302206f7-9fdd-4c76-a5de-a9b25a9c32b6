// <PERSON><PERSON>er Console <PERSON> to Ingest Document for AI Chat
// Copy and paste this into your browser console while on your Overleaf project page

(async function ingestDocumentForAI() {
  console.log('🚀 Starting AI Document Ingestion...');

  // Get project ID from URL
  const projectId = window.location.pathname.match(/\/project\/([a-f0-9]{24})/)?.[1];
  if (!projectId) {
    console.error('❌ Could not find project ID in URL');
    return;
  }

  console.log('✅ Project ID:', projectId);

  // Your actual document content from the screenshot
  const documentContent = `
\\documentclass{article}
\\usepackage[english]{babel}
\\usepackage{amsmath}
\\usepackage{amssymb}
\\usepackage{geometry}
\\usepackage{letterpaper,top=2cm,bottom=2cm,left=3cm,right=3cm,marginparwidth=1.75cm}

\\title{Your Paper}
\\author{You}

\\begin{document}
\\maketitle

\\section{Introduction}
Your introduction goes here! Simply start writing your document and use the Recompile button to view the updated PDF preview. Examples of commonly used commands and features are listed below, to help you get started.

Once you're familiar with the editor, you can find various project settings in the Overleaf menu, accessed via the button in the very top left of the editor.

\\section{How to create Sections and Subsections}
Simply use the section and subsection commands, as in this example document! With Overleaf, all the formatting and numbering is handled automatically.

\\subsection{How to include Figures}
First you have to upload the image file from your computer using the upload link in the file-tree menu. Then use the includegraphics command to include it in your document. Use the figure environment and the caption command to add a number and a caption to your figure. See the code for Figure \\ref{fig:frog} in this section for an example.

Note that your figure will automatically be placed in the most appropriate place for it, given the surrounding text and taking into account other figures or tables that may be close by. You can find out more about adding images to your documents in this help article on including images on Overleaf.

\\begin{figure}
\\centering
\\includegraphics[width=0.3\\textwidth]{frog.jpg}
\\caption{\\label{fig:frog}This frog was uploaded via the file-tree menu.}
\\end{figure}

\\subsection{How to add Tables}
Use the table and tabular environments for basic tables — see Table~\\ref{tab:widgets}, for example. For more information, please see this help article on tables.

\\begin{table}
\\centering
\\begin{tabular}{l|r}
Item & Quantity \\\\
\\hline
Widgets & 42 \\\\
Gadgets & 13
\\end{tabular}
\\caption{\\label{tab:widgets}An example table.}
\\end{table}

\\subsection{How to add Comments and Track Changes}
Comments can be added to your project by highlighting some text and clicking "Add comment" in the top right of the editor. To view existing comments, click on the Review menu in the toolbar above. To track changes, click on Track Changes in the Review menu.

\\subsection{How to add Lists}
You can make lists with:

\\begin{itemize}
\\item Like this,
\\item and like this.
\\end{itemize}

\\dots or with numbers:

\\begin{enumerate}
\\item Like this,
\\item and like this.
\\end{enumerate}

\\subsection{How to write Mathematics}
\\LaTeX{} is great at typesetting mathematics. Let $X_1, X_2, \\ldots, X_n$ be a sequence of independent and identically distributed random variables with $\\text{E}[X_i] = \\mu$ and $\\text{Var}(X_i) = \\sigma^2 < \\infty$, and let
$$S_n = \\frac{X_1 + X_2 + \\cdots + X_n}{n} = \\frac{1}{n}\\sum_{i=1}^{n} X_i$$
denote their mean. Then as $n$ approaches infinity, the random variables $\\sqrt{n}(S_n - \\mu)$ converge in distribution to a normal $N(0, \\sigma^2)$.

\\subsection{How to change the margins and paper size}
Usually the template you're using will have the page margins and paper size set correctly for that use-case. For example, if you're using a journal article template provided by the journal publisher, that template will be formatted according to their requirements. In these cases, it's best not to alter the margins directly.

If however you're using a more general template, such as this one, and would like to alter the margins, a common way to do so is via the geometry package. You can find the geometry package loaded in the preamble at the top of this example file, and if you'd like to learn more about how to adjust the settings, please visit this help article on page size and margins.

\\subsection{How to change the document language and spell check language}
Overleaf supports many different languages, including multiple different languages within one document.

The babel package, whose options are set in the preamble at the top of this example file, can be used to change the document language. To change the spell check language, simply open the Overleaf menu at the top left of the editor window, scroll down to the spell check setting, and adjust the Language setting.

The Overleaf menu can also be used to change the main document font, the font size, and the document class (which can, for example, be used to format an academic thesis, journal article, book chapter, etc).

\\subsection{How to add Citations and a References List}
You can simply upload a .bib file containing your BibTeX entries, and then cite them like this: \\cite{greenwade93}. The citations will then be processed and a reference list generated automatically. For more information about BibTeX and bibliography management, see the help article on using bibliographies on Overleaf.

\\subsection{Good luck!}
We hope you find Overleaf useful for your projects, and please let us know if you have any feedback. Further help documentation is available at help.overleaf.com.

\\bibliographystyle{alpha}
\\bibliography{sample}

\\end{document}
  `.trim();

  try {
    console.log('🔄 Sending document to AI ingestion endpoint...');

    // Get CSRF token from meta tag
    const csrfToken = document.querySelector('meta[name="ol-csrfToken"]')?.getAttribute('content');
    if (!csrfToken) {
      console.error('❌ Could not find CSRF token');
      return;
    }

    // Make the API request
    const response = await fetch(`/project/${projectId}/ai/ingest-document`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': csrfToken,
      },
      body: JSON.stringify({
        content: documentContent,
        documentType: 'latex'
      })
    });

    console.log('📡 Response status:', response.status);

    if (response.ok) {
      const result = await response.json();
      console.log('✅ Document ingestion successful!');
      console.log('📊 Result:', result);
      console.log(`🎉 Created ${result.chunksCount} chunks for AI processing`);

      console.log('');
      console.log('🎯 Next Steps:');
      console.log('1. Open the chat panel (if not already open)');
      console.log('2. Send AI queries like:');
      console.log('   • "@ai explain this document"');
      console.log('   • "@ai what is the main equation?"');
      console.log('   • "@ai summarize the methodology section"');
      console.log('   • "explain the Gaussian integral"');

    } else {
      const errorText = await response.text();
      console.error('❌ Document ingestion failed');
      console.error('Status:', response.status);
      console.error('Response:', errorText);

      if (response.status === 403) {
        console.log('💡 This might be a CSRF token issue. Try refreshing the page and running the script again.');
      }
    }

  } catch (error) {
    console.error('❌ Error during document ingestion:', error);
  }
})();

// Instructions for use:
console.log(`
🔧 HOW TO USE THIS SCRIPT:

1. Open your Overleaf project: http://localhost/project/6885bb82d8b9e3e61aa76d5d
2. Open browser developer tools (F12)
3. Go to the Console tab
4. IMPORTANT: Replace the documentContent variable above with your actual LaTeX document content
5. Copy and paste this entire script into the console
6. Press Enter to run it
7. Check the console output for success/error messages
8. If successful, try sending AI queries in the chat like "@ai explain this document"

📝 To customize the document content:
- Edit the documentContent variable in the script above
- Replace it with your actual LaTeX document content
- Make sure to escape any backslashes properly (use \\\\ instead of \\)
`);
