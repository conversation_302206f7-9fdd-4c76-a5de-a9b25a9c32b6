#!/usr/bin/env node

const http = require('http');
const { URL } = require('url');

const config = {
  projectId: '6885bb82d8b9e3e61aa76d5d', // Your existing project
  baseUrl: 'http://localhost:80',
  email: '<EMAIL>',
  password: '12345678'
};

let sessionCookie = null;
let csrfToken = null;

function makeRequest(options, postData = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(options.path, config.baseUrl);
    const requestOptions = {
      hostname: url.hostname,
      port: url.port || 80,
      path: url.pathname + url.search,
      method: options.method || 'GET',
      headers: options.headers || {},
    };

    const req = http.request(requestOptions, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: data,
        });
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (postData) {
      req.write(postData);
    }
    req.end();
  });
}

async function registerUser() {
  console.log('🔄 Attempting to register test user...');

  // First get the register page to extract CSRF token
  const registerPageRes = await makeRequest({ path: '/register' });

  if (registerPageRes.statusCode !== 200) {
    console.error(`❌ Failed to get register page! Status: ${registerPageRes.statusCode}`);
    return false;
  }

  // Extract CSRF token from the register page
  let csrfMatch = registerPageRes.data.match(/name="_csrf" value="([^"]+)"/);
  if (!csrfMatch) {
    csrfMatch = registerPageRes.data.match(/name="ol-csrfToken" content="([^"]+)"/);
  }
  if (!csrfMatch) {
    console.error('❌ Could not find CSRF token in register page');
    return false;
  }

  const registerCsrfToken = csrfMatch[1];
  console.log('✅ Register CSRF token extracted:', registerCsrfToken.substring(0, 20) + '...');

  // Extract initial cookies
  const initialCookies = registerPageRes.headers['set-cookie']
    ? registerPageRes.headers['set-cookie'].join('; ')
    : '';

  // Perform registration
  const registerData = `email=${encodeURIComponent(config.email)}&password=${encodeURIComponent(config.password)}&_csrf=${encodeURIComponent(registerCsrfToken)}`;

  const registerRes = await makeRequest(
    {
      path: '/register',
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Length': Buffer.byteLength(registerData),
        Cookie: initialCookies,
      },
    },
    registerData
  );

  if (registerRes.statusCode === 200 || (registerRes.statusCode >= 300 && registerRes.statusCode < 400)) {
    console.log('✅ Registration successful (or user already exists)');
    return true;
  } else {
    console.log(`⚠️ Registration response: Status ${registerRes.statusCode}, continuing with login attempt...`);
    return true; // Continue anyway, user might already exist
  }
}

async function login() {
  console.log('🔄 Attempting to login...');

  // First get the login page to extract CSRF token
  const loginPageRes = await makeRequest({ path: '/login' });

  if (loginPageRes.statusCode !== 200) {
    console.error(`❌ Failed to get login page! Status: ${loginPageRes.statusCode}`);
    return false;
  }

  // Extract CSRF token from the login page (try both methods)
  let csrfMatch = loginPageRes.data.match(/name="_csrf" value="([^"]+)"/);
  if (!csrfMatch) {
    csrfMatch = loginPageRes.data.match(/name="ol-csrfToken" content="([^"]+)"/);
  }
  if (!csrfMatch) {
    console.error('❌ Could not find CSRF token in login page');
    return false;
  }

  csrfToken = csrfMatch[1];
  console.log('✅ CSRF token extracted:', csrfToken.substring(0, 20) + '...');

  // Extract initial cookies
  const initialCookies = loginPageRes.headers['set-cookie']
    ? loginPageRes.headers['set-cookie'].join('; ')
    : '';

  // Perform login
  const loginData = `email=${encodeURIComponent(config.email)}&password=${encodeURIComponent(config.password)}&_csrf=${encodeURIComponent(csrfToken)}`;

  const loginRes = await makeRequest(
    {
      path: '/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Length': Buffer.byteLength(loginData),
        Cookie: initialCookies,
      },
    },
    loginData
  );

  if (loginRes.statusCode === 200 || (loginRes.statusCode >= 300 && loginRes.statusCode < 400)) {
    sessionCookie = loginRes.headers['set-cookie']
      ? loginRes.headers['set-cookie'].join('; ')
      : initialCookies;
    console.log('✅ Login successful');
    return true;
  } else {
    console.error(`❌ Login failed! Status: ${loginRes.statusCode}`);
    console.error(`   Response: ${loginRes.data}`);
    return false;
  }
}

async function testChatMessage() {
  console.log('🔄 Testing chat message...');

  const messageData = JSON.stringify({
    content: "@ai what is the main equation in this document?",
    client_id: "test-123"
  });

  const options = {
    path: `/project/${config.projectId}/messages`,
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(messageData),
      Cookie: sessionCookie,
      'X-CSRF-Token': csrfToken,
    },
  };

  try {
    const response = await makeRequest(options, messageData);
    console.log('✅ Chat message test completed');
    console.log('Status:', response.statusCode);
    console.log('Response:', response.data);
    return response.statusCode === 204; // Success status for chat messages
  } catch (error) {
    console.error('❌ Chat message test failed:', error.message);
    return false;
  }
}

async function testDocumentIngestion() {
  console.log('🔄 Testing document ingestion...');

  const sampleDocument = `
\\documentclass{article}
\\usepackage{amsmath}
\\title{AI Test Document}
\\author{Test User}
\\begin{document}
\\maketitle

\\section{Introduction}
This is a test LaTeX document for AI processing. It contains mathematical equations and structured content.

\\section{Mathematics}
Here is Einstein's famous equation:
\\begin{equation}
E = mc^2
\\end{equation}

\\section{Lists}
Here are some important concepts:
\\begin{itemize}
\\item Artificial Intelligence
\\item Machine Learning
\\item Natural Language Processing
\\end{itemize}

\\section{Conclusion}
This document demonstrates various LaTeX features that the AI should be able to understand and explain.
\\end{document}
  `.trim();

  const postData = JSON.stringify({
    content: sampleDocument,
    documentType: 'latex'
  });

  const options = {
    path: `/project/${config.projectId}/ai/ingest-document`,
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData),
      Cookie: sessionCookie,
      'X-CSRF-Token': csrfToken,
    },
  };

  try {
    const response = await makeRequest(options, postData);
    console.log('✅ Document ingestion test completed');
    console.log('Status:', response.statusCode);
    console.log('Response:', response.data);
    return response.statusCode === 200;
  } catch (error) {
    console.error('❌ Document ingestion test failed:', error.message);
    return false;
  }
}

async function testAiStatus() {
  console.log('🔄 Testing AI status endpoint...');

  const options = {
    path: `/project/${config.projectId}/document/ai-status`,
    method: 'GET',
    headers: {
      Cookie: sessionCookie,
      'X-CSRF-Token': csrfToken,
    },
  };

  try {
    const response = await makeRequest(options);
    console.log('✅ AI status test completed');
    console.log('Status:', response.statusCode);
    console.log('Response:', response.data);
    return response.statusCode === 200;
  } catch (error) {
    console.error('❌ AI status test failed:', error.message);
    return false;
  }
}

async function createProject() {
  console.log('🔄 Creating a new test project...');

  const projectData = JSON.stringify({
    projectName: 'AI Test Project',
    template: 'none'
  });

  const options = {
    path: '/project/new',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(projectData),
      Cookie: sessionCookie,
      'X-CSRF-Token': csrfToken,
    },
  };

  try {
    const response = await makeRequest(options, projectData);
    console.log('✅ Project creation response received');
    console.log('Status:', response.statusCode);

    if (response.statusCode === 200) {
      // Try to extract project ID from response
      const responseData = JSON.parse(response.data);
      if (responseData.project_id) {
        config.projectId = responseData.project_id;
        console.log('✅ Created project with ID:', config.projectId);
        return true;
      }
    } else if (response.statusCode >= 300 && response.statusCode < 400) {
      // Handle redirect - extract project ID from Location header
      const location = response.headers.location;
      if (location) {
        const projectMatch = location.match(/\/project\/([a-f0-9]{24})/);
        if (projectMatch) {
          config.projectId = projectMatch[1];
          console.log('✅ Created project with ID:', config.projectId);
          return true;
        }
      }
    }

    console.log('⚠️ Could not extract project ID from response');
    return false;
  } catch (error) {
    console.error('❌ Failed to create project:', error.message);
    return false;
  }
}

async function listProjects() {
  console.log('🔄 Listing user projects...');

  const options = {
    path: '/project',
    method: 'GET',
    headers: {
      Cookie: sessionCookie,
    },
  };

  try {
    const response = await makeRequest(options);
    console.log('✅ Projects list retrieved');
    console.log('Status:', response.statusCode);

    // Try to extract project IDs from the response
    const projectMatches = response.data.match(/\/project\/([a-f0-9]{24})/g);
    if (projectMatches) {
      const projectIds = projectMatches.map(match => match.replace('/project/', ''));
      console.log('Found project IDs:', [...new Set(projectIds)]);

      // Use the first project ID if available
      if (projectIds.length > 0) {
        config.projectId = projectIds[0];
        console.log('✅ Using project ID:', config.projectId);
        return true;
      }
    }

    console.log('⚠️ No projects found for this user');
    return false;
  } catch (error) {
    console.error('❌ Failed to list projects:', error.message);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting AI Chat Tests');
  console.log('============================================================');

  if (!(await login())) {
    console.error('❌ Login failed. Stopping tests.');
    process.exit(1);
  }

  console.log('============================================================');

  // Try to find an existing project
  let hasProjects = await listProjects();
  if (!hasProjects) {
    console.log('⚠️ No accessible projects found. Attempting to create a new project...');
    hasProjects = await createProject();
    if (!hasProjects) {
      console.log('❌ Could not create a project. Please create a project manually in Overleaf.');
      process.exit(1);
    }
  }

  console.log('============================================================');

  // Test document ingestion first
  const ingestionResult = await testDocumentIngestion();

  // Wait a moment for ingestion to complete
  if (ingestionResult) {
    console.log('⏳ Waiting 3 seconds for document processing...');
    await new Promise(resolve => setTimeout(resolve, 3000));
  }

  const statusResult = await testAiStatus();
  const chatResult = await testChatMessage();

  console.log('============================================================');
  console.log('📊 Test Results:');
  console.log(`   Document Ingestion: ${ingestionResult ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`   AI Status: ${statusResult ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`   Chat Message: ${chatResult ? '✅ PASS' : '❌ FAIL'}`);

  if (ingestionResult && statusResult && chatResult) {
    console.log('✅ All tests passed successfully!');
    console.log('');
    console.log('🎉 Your AI document chat is now working!');
    console.log('💡 You can now:');
    console.log('   1. Open your project: http://localhost/project/6885bb82d8b9e3e61aa76d5d');
    console.log('   2. Send AI queries in chat like: "@ai explain this document"');
    console.log('   3. Ask questions like: "what is the main equation in this document?"');
  } else {
    console.log('❌ Some tests failed. Check the output above for details.');
    process.exit(1);
  }
}

runTests().catch(error => {
  console.error('An unexpected error occurred:', error);
  process.exit(1);
});
