// Test if the web service responds to regular messages
fetch('http://localhost/project/6885bb82d8b9e3e61aa76d5d/messages', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-CSRF-Token': 'zbf3Dint-QCEoUYtRNT9BJZWvHFKPhuRuiQw',
    'Cookie': 'overleaf.sid=s%3Ac2DapR3JPu6HYPc13deFDEZiJKWGoVIK.Uf6v9KJ7muvIAvyq2fDGOvOuvuVuRyyLO0xZmKX6pg8'
  },
  body: JSON.stringify({
    content: "test message",
    user_id: "6885bb5fd8b9e3e61aa76d42", // Add your user ID
    client_id: "test-123"
  })
})
.then(response => {
  console.log('Status:', response.status);
  console.log('Headers:', [...response.headers.entries()]);
  return response.text();
})
.then(data => console.log('Response:', data))
.catch(error => console.error('Error:', error));
