#!/usr/bin/env node

const http = require('http');
const { URL } = require('url');

const config = {
  projectId: '6885bb82d8b9e3e61aa76d5d',
  baseUrl: 'http://localhost:80',
  email: '<EMAIL>',
  password: '12345678'
};

let sessionCookie = null;
let csrfToken = null;

function makeRequest(options, postData = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(options.path, config.baseUrl);
    const requestOptions = {
      hostname: url.hostname,
      port: url.port || 80,
      path: url.pathname + url.search,
      method: options.method || 'GET',
      headers: options.headers || {},
    };

    const req = http.request(requestOptions, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: data,
        });
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (postData) {
      req.write(postData);
    }
    req.end();
  });
}

async function login() {
  console.log('🔄 Logging in...');
  
  const loginPageRes = await makeRequest({ path: '/login' });
  let csrfMatch = loginPageRes.data.match(/name="_csrf" value="([^"]+)"/);
  if (!csrfMatch) {
    csrfMatch = loginPageRes.data.match(/name="ol-csrfToken" content="([^"]+)"/);
  }
  if (!csrfMatch) {
    throw new Error('Could not find CSRF token');
  }
  
  csrfToken = csrfMatch[1];
  const initialCookies = loginPageRes.headers['set-cookie']
    ? loginPageRes.headers['set-cookie'].join('; ')
    : '';

  const loginData = `email=${encodeURIComponent(config.email)}&password=${encodeURIComponent(config.password)}&_csrf=${encodeURIComponent(csrfToken)}`;

  const loginRes = await makeRequest(
    {
      path: '/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Length': Buffer.byteLength(loginData),
        Cookie: initialCookies,
      },
    },
    loginData
  );

  if (loginRes.statusCode === 200 || (loginRes.statusCode >= 300 && loginRes.statusCode < 400)) {
    sessionCookie = loginRes.headers['set-cookie']
      ? loginRes.headers['set-cookie'].join('; ')
      : initialCookies;
    console.log('✅ Login successful');
    return true;
  } else {
    throw new Error(`Login failed: ${loginRes.statusCode}`);
  }
}

async function ingestDocument() {
  console.log('🔄 Ingesting document for AI processing...');
  
  // Sample LaTeX document - replace this with your actual document content
  const sampleDocument = `
\\documentclass{article}
\\usepackage{amsmath}
\\usepackage{graphicx}
\\title{My Research Paper}
\\author{Your Name}
\\date{\\today}

\\begin{document}
\\maketitle

\\section{Introduction}
This document contains important research findings and mathematical formulations.

\\section{Methodology}
We used advanced techniques to analyze the data.

\\section{Results}
The key equation we derived is:
\\begin{equation}
\\int_0^\\infty e^{-x^2} dx = \\frac{\\sqrt{\\pi}}{2}
\\end{equation}

\\section{Conclusion}
Our findings show significant improvements in the proposed method.

\\end{document}
  `.trim();

  const postData = JSON.stringify({
    content: sampleDocument,
    documentType: 'latex'
  });

  const options = {
    path: `/project/${config.projectId}/ai/ingest-document`,
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData),
      Cookie: sessionCookie,
      'X-CSRF-Token': csrfToken,
    },
  };

  try {
    const response = await makeRequest(options, postData);
    console.log('✅ Document ingestion completed');
    console.log('Status:', response.statusCode);
    console.log('Response:', response.data);
    
    if (response.statusCode === 200) {
      const result = JSON.parse(response.data);
      console.log('🎉 Success! Document processed for AI assistance');
      console.log(`📊 Created ${result.chunksCount} chunks for AI processing`);
      return true;
    } else {
      console.log('⚠️ Ingestion may have failed. Check the response above.');
      return false;
    }
  } catch (error) {
    console.error('❌ Document ingestion failed:', error.message);
    return false;
  }
}

async function main() {
  try {
    console.log('🚀 Starting Document Ingestion for AI Chat');
    console.log('============================================================');
    
    await login();
    const success = await ingestDocument();
    
    console.log('============================================================');
    if (success) {
      console.log('✅ Document successfully prepared for AI chat!');
      console.log('');
      console.log('🎯 Next Steps:');
      console.log('1. Open your project: http://localhost/project/6885bb82d8b9e3e61aa76d5d');
      console.log('2. Open the chat panel');
      console.log('3. Send AI queries like:');
      console.log('   • "@ai explain this document"');
      console.log('   • "@ai what is the main equation?"');
      console.log('   • "@ai summarize the methodology section"');
      console.log('   • "explain this equation"');
    } else {
      console.log('❌ Document ingestion failed. Check the errors above.');
    }
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

main();
