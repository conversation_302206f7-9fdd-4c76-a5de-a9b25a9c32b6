#! /usr/bin/env bash

set -euo pipefail

#### Detect Toolkit Project Root ####
# if realpath is not available, create a semi-equivalent function
command -v realpath >/dev/null 2>&1 || realpath() {
  [[ $1 = /* ]] && echo "$1" || echo "$PWD/${1#./}"
}
SCRIPT_PATH="$(realpath "${BASH_SOURCE[0]}")"
SCRIPT_DIR="$(dirname "$SCRIPT_PATH")"
TOOLKIT_ROOT="$(realpath "$SCRIPT_DIR/..")"
if [[ ! -d "$TOOLKIT_ROOT/bin" ]] || [[ ! -d "$TOOLKIT_ROOT/config" ]]; then
  echo "ERROR: could not find root of overleaf-toolkit project (inferred project root as '$TOOLKIT_ROOT')"
  exit 1
fi

source "$TOOLKIT_ROOT/lib/shared-functions.sh"

function __main__() {
  read_mongo_version
  exec env SKIP_WARNINGS=true "$TOOLKIT_ROOT/bin/docker-compose" exec mongo $MONGOSH sharelatex
}

__main__ "$@"
