#! /usr/bin/env bash

set -euo pipefail

#### Detect Toolkit Project Root ####
# if realpath is not available, create a semi-equivalent function
command -v realpath >/dev/null 2>&1 || realpath() {
  [[ $1 = /* ]] && echo "$1" || echo "$PWD/${1#./}"
}
SCRIPT_PATH="$(realpath "${BASH_SOURCE[0]}")"
SCRIPT_DIR="$(dirname "$SCRIPT_PATH")"
TOOLKIT_ROOT="$(realpath "$SCRIPT_DIR/..")"
if [[ ! -d "$TOOLKIT_ROOT/bin" ]] || [[ ! -d "$TOOLKIT_ROOT/config" ]]; then
  echo "ERROR: could not find root of overleaf-toolkit project (inferred project root as '$TOOLKIT_ROOT')"
  exit 1
fi

function usage() {
    echo "Usage: bin/error-logs [OPTIONS] [SERVICES...]"
    echo ""
    echo "Services:  chat, clsi, contacts, docstore, document-updater,"
    echo "           filestore, notifications, real-time, spelling,"
    echo "           tags, track-changes, web, project-history, history-v1"
    echo ""
    echo "Options:"
    echo "  -f              follow log output"
    echo "  -n {number}     number of lines to print (default 20)"
    echo ""
    echo "Examples:"
    echo ""
    echo "    bin/error-logs -n 50 web clsi"
    echo ""
    echo "    bin/error-logs -f web"
    echo ""
    echo "    bin/error-logs -f web chat docstore"
    echo ""
    echo "    bin/error-logs -n 100 -f filestore "
    echo ""
    echo "    bin/error-logs -f"
}

function __main__() {
    if [[ "${1:-null}" == "help" ]] || [[ "${1:-null}" == "--help" ]]; then
        usage
        exit
    fi
    # preserve the '==>' labels from tail output
    "$TOOLKIT_ROOT/bin/logs" "$@" | grep -E '(^==>.*$|^.*level":50.*$)' --color=never
}

__main__ "$@"
