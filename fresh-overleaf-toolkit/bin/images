#! /usr/bin/env bash

set -uo pipefail

#### Detect Toolkit Project Root ####
# if realpath is not available, create a semi-equivalent function
command -v realpath >/dev/null 2>&1 || realpath() {
  [[ $1 = /* ]] && echo "$1" || echo "$PWD/${1#./}"
}
SCRIPT_PATH="$(realpath "${BASH_SOURCE[0]}")"
SCRIPT_DIR="$(dirname "$SCRIPT_PATH")"
TOOLKIT_ROOT="$(realpath "$SCRIPT_DIR/..")"
if [[ ! -d "$TOOLKIT_ROOT/bin" ]] || [[ ! -d "$TOOLKIT_ROOT/config" ]]; then
  echo "ERROR: could not find root of overleaf-toolkit project (inferred project root as '$TOOLKIT_ROOT')"
  exit 1
fi


function usage() {
    echo "Usage: bin/images"
    echo ""
    echo "Prints information about overleaf docker images on the system"
}

function __main__() {
    if [[ "${1:-null}" == "help" ]] \
        || [[ "${1:-null}" == "--help" ]] ; then
        usage && exit
    fi

    echo "---- Community Edition Images ----"
    docker images sharelatex/sharelatex
    echo "---- Server Pro Images ----"
    docker images quay.io/sharelatex/sharelatex-pro
    echo "---- TexLive Images ----"
    docker images quay.io/sharelatex/texlive-full
    echo "---- Git Bridge Images ----"
    docker images quay.io/sharelatex/git-bridge
}

__main__ "$@"
