#! /usr/bin/env bash
# shellcheck source-path=..

set -euo pipefail

#### Detect Toolkit Project Root ####
# if realpath is not available, create a semi-equivalent function
command -v realpath >/dev/null 2>&1 || realpath() {
  [[ $1 = /* ]] && echo "$1" || echo "$PWD/${1#./}"
}
SCRIPT_PATH="$(realpath "${BASH_SOURCE[0]}")"
SCRIPT_DIR="$(dirname "$SCRIPT_PATH")"
TOOLKIT_ROOT="$(realpath "$SCRIPT_DIR/..")"
if [[ ! -d "$TOOLKIT_ROOT/bin" ]] || [[ ! -d "$TOOLKIT_ROOT/config" ]]; then
  echo "ERROR: could not find root of overleaf-toolkit project (inferred project root as '$TOOLKIT_ROOT')"
  exit 1
fi

source "$TOOLKIT_ROOT/lib/shared-functions.sh"

function usage() {
  echo "Usage: bin/rename-rc-vars"
  echo ""
  echo "Updates config/overleaf.rc to ensure variables are renamed from 'SH<PERSON><PERSON>ATEX_' to 'OVERLEAF_'"
  echo ""
}

function __main__() {
  if [[ "${1:-null}" == "help" ]] || [[ "${1:-null}" == "--help" ]]; then
    usage
    exit
  fi

  echo "This script will update your config/overleaf.rc."
  echo "We recommend backing up your config with bin/backup-config."

  rebrand_sharelatex_env_variables 'overleaf.rc'

  echo "Done."
}

__main__ "$@"
