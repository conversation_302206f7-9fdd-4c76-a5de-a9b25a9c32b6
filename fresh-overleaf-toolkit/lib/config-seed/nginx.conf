events {}

http {

    server {
        listen 80 default_server;
        server_name _;
        return 301 https://$host$request_uri;
    }


    server {
        listen 443 ssl;

        ssl_certificate      /certs/nginx_certificate.pem;
        ssl_certificate_key  /certs/nginx_key.pem;

        # Intermediate Mozilla Config
        # https://ssl-config.mozilla.org/#server=nginx&version=1.26.0&config=intermediate&openssl=1.1.1w&ocsp=false&guideline=5.7
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384:DHE-RSA-CHACHA20-POLY1305;
        ssl_prefer_server_ciphers off;

        # config to enable HSTS(HTTP Strict Transport Security) https://developer.mozilla.org/en-US/docs/Security/HTTP_Strict_Transport_Security
        # to avoid ssl stripping https://en.wikipedia.org/wiki/SSL_stripping#SSL_stripping	
        add_header Strict-Transport-Security "max-age=31536000; includeSubdomains;";

        server_tokens off;

        client_max_body_size 50M;

        location / {
            proxy_pass http://sharelatex:80;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_read_timeout 3m;
            proxy_send_timeout 3m;
        }
    }
}