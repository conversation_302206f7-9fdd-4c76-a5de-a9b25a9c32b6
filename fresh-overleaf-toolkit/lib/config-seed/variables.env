OVERLEAF_APP_NAME="Our Overleaf Instance"

ENABLED_LINKED_FILE_TYPES=project_file,project_output_file

# Enables Thumbnail generation using ImageMagick
ENABLE_CONVERSIONS=true

# Disables email confirmation requirement
EMAIL_CONFIRMATION_DISABLED=true

## Nginx
# NGINX_WORKER_PROCESSES=4
# NGINX_WORKER_CONNECTIONS=768

## Set for TLS via nginx-proxy
# OVERLEAF_BEHIND_PROXY=true
# OVERLEAF_SECURE_COOKIE=true

# OVERLEAF_SITE_URL=http://overleaf.example.com
# OVERLEAF_NAV_TITLE=Our Overleaf Instance
# OVERLEAF_HEADER_IMAGE_URL=http://somewhere.com/mylogo.png
# OVERLEAF_ADMIN_EMAIL=<EMAIL>

# OVERLEAF_LEFT_FOOTER='[{"text": "Contact your support team", "url": "mailto:<EMAIL>"}]'
# OVERLEAF_RIGHT_FOOTER='[{"text": "Hello, I am on the Right"}]'

# OVERLEAF_EMAIL_FROM_ADDRESS=<EMAIL>

# OVERLEAF_EMAIL_AWS_SES_ACCESS_KEY_ID=
# OVERLEAF_EMAIL_AWS_SES_SECRET_KEY=

# OVERLEAF_EMAIL_SMTP_HOST=smtp.example.com
# OVERLEAF_EMAIL_SMTP_PORT=587
# OVERLEAF_EMAIL_SMTP_SECURE=false
# OVERLEAF_EMAIL_SMTP_USER=
# OVERLEAF_EMAIL_SMTP_PASS=
# OVERLEAF_EMAIL_SMTP_NAME=
# OVERLEAF_EMAIL_SMTP_LOGGER=false
# OVERLEAF_EMAIL_SMTP_TLS_REJECT_UNAUTH=true
# OVERLEAF_EMAIL_SMTP_IGNORE_TLS=false
# OVERLEAF_CUSTOM_EMAIL_FOOTER=This system is run by department x

################
## Server Pro ##
################

EXTERNAL_AUTH=none
# OVERLEAF_LDAP_URL=ldap://ldap:389
# OVERLEAF_LDAP_SEARCH_BASE=ou=people,dc=planetexpress,dc=com
# OVERLEAF_LDAP_SEARCH_FILTER=(uid={{username}})
# OVERLEAF_LDAP_BIND_DN=cn=admin,dc=planetexpress,dc=com
# OVERLEAF_LDAP_BIND_CREDENTIALS=GoodNewsEveryone
# OVERLEAF_LDAP_EMAIL_ATT=mail
# OVERLEAF_LDAP_NAME_ATT=cn
# OVERLEAF_LDAP_LAST_NAME_ATT=sn
# OVERLEAF_LDAP_UPDATE_USER_DETAILS_ON_LOGIN=true

# OVERLEAF_TEMPLATES_USER_ID=578773160210479700917ee5
# OVERLEAF_NEW_PROJECT_TEMPLATE_LINKS=[{"name":"All Templates","url":"/templates/all"}]

# TEX_LIVE_DOCKER_IMAGE=quay.io/sharelatex/texlive-full:2022.1
# ALL_TEX_LIVE_DOCKER_IMAGES=quay.io/sharelatex/texlive-full:2022.1,quay.io/sharelatex/texlive-full:2021.1,quay.io/sharelatex/texlive-full:2020.1

# OVERLEAF_PROXY_LEARN=true

# S3
# Docs: https://github.com/overleaf/overleaf/wiki/S3
# ## Enable the s3 backend for filestore
# OVERLEAF_FILESTORE_BACKEND=s3
# ## Enable S3 backend for history
# OVERLEAF_HISTORY_BACKEND=s3
# #
# # Pick one of the two sections "AWS S3" or "Self-hosted S3".
# #
# # AWS S3
# ## Bucket name for project files
# OVERLEAF_FILESTORE_USER_FILES_BUCKET_NAME=overleaf-user-files
# ## Bucket name for template files
# OVERLEAF_FILESTORE_TEMPLATE_FILES_BUCKET_NAME=overleaf-template-files
# ## Key for filestore user
# OVERLEAF_FILESTORE_S3_ACCESS_KEY_ID=...
# ## Secret for filestore user
# OVERLEAF_FILESTORE_S3_SECRET_ACCESS_KEY=...
# ## Bucket region you picked when creating the buckets.
# OVERLEAF_FILESTORE_S3_REGION=""
# ## Bucket name for project history blobs
# OVERLEAF_HISTORY_PROJECT_BLOBS_BUCKET=overleaf-project-blobs
# ## Bucket name for history chunks
# OVERLEAF_HISTORY_CHUNKS_BUCKET=overleaf-chunks
# ## Key for history user
# OVERLEAF_HISTORY_S3_ACCESS_KEY_ID=...
# ## Secret for history user
# OVERLEAF_HISTORY_S3_SECRET_ACCESS_KEY=...
# ## Bucket region you picked when creating the buckets.
# OVERLEAF_HISTORY_S3_REGION=""
#
# # Self-hosted S3
# ## Bucket name for project files
# OVERLEAF_FILESTORE_USER_FILES_BUCKET_NAME=overleaf-user-files
# ## Bucket name for template files
# OVERLEAF_FILESTORE_TEMPLATE_FILES_BUCKET_NAME=overleaf-template-files
# ## Key for filestore user
# OVERLEAF_FILESTORE_S3_ACCESS_KEY_ID=...
# ## Secret for filestore user
# OVERLEAF_FILESTORE_S3_SECRET_ACCESS_KEY=...
# ## S3 provider endpoint
# OVERLEAF_FILESTORE_S3_ENDPOINT=http://***********:9000
# ## Path style addressing of buckets. Most likely you need to set this to "true".
# OVERLEAF_FILESTORE_S3_PATH_STYLE="true"
# ## Bucket region. Most likely you do not need to configure this.
# OVERLEAF_FILESTORE_S3_REGION=""
# ## Bucket name for project history blobs
# OVERLEAF_HISTORY_PROJECT_BLOBS_BUCKET=overleaf-project-blobs
# ## Bucket name for history chunks
# OVERLEAF_HISTORY_CHUNKS_BUCKET=overleaf-chunks
# ## Key for history user
# OVERLEAF_HISTORY_S3_ACCESS_KEY_ID=...
# ## Secret for history user
# OVERLEAF_HISTORY_S3_SECRET_ACCESS_KEY=...
# ## S3 provider endpoint
# OVERLEAF_HISTORY_S3_ENDPOINT=http://***********:9000
# ## Path style addressing of buckets. Most likely you need to set this to "true".
# OVERLEAF_HISTORY_S3_PATH_STYLE="true"
# ## Bucket region. Most likely you do not need to configure this.
# OVERLEAF_HISTORY_S3_REGION=""
