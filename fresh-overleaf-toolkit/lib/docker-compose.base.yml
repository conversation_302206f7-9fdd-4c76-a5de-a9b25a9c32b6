---
services:

    sharelatex:
        restart: always
        image: "${IMAGE}"
        container_name: sharelatex
        volumes:
            - "${OVERLEAF_DATA_PATH}:${OVERLEAF_IN_CONTAINER_DATA_PATH}"
        ports:
            - "${OVERLEAF_LISTEN_IP:-127.0.0.1}:${OVERLEAF_PORT:-80}:80"
        environment:
          GIT_BRIDGE_ENABLED: "${GIT_BRIDGE_ENABLED}"
          GIT_BRIDGE_HOST: "git-bridge"
          GIT_BRIDGE_PORT: "8000"
          REDIS_HOST: "${REDIS_HOST}"
          REDIS_PORT: "${REDIS_PORT}"
          V1_HISTORY_URL: "http://sharelatex:3100/api"
        env_file:
            - ../config/variables.env
        stop_grace_period: 60s
