---
services:

  nginx:
    image: "${NGINX_IMAGE}"
    ports:
      - "${NGINX_TLS_LISTEN_IP:-0.0.0.0}:${TLS_PORT:-443}:443"
      - "${NGINX_HTTP_LISTEN_IP:-*********}:${NGINX_HTTP_PORT:-80}:80"
    volumes:
      - "${TLS_PRIVATE_KEY_PATH}:/certs/nginx_key.pem:ro"
      - "${TLS_CERTIFICATE_PATH}:/certs/nginx_certificate.pem:ro"
      - "${NGINX_CONFIG_PATH}:/etc/nginx/nginx.conf:ro"
    restart: always
    container_name: nginx
    depends_on:
      - sharelatex
