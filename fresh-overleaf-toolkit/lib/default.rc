# Default values for config/overleaf.rc

PROJECT_NAME=overleaf
SERVER_PRO=false
OVERLEAF_DATA_PATH=data/sharelatex
OVERLEAF_PORT=80

# Sibling containers
SIBLING_CONTAINERS_ENABLED=false
DOCKER_SOCKET_PATH=/var/run/docker.sock

# Mongo configuration
MONGO_ENABLED=true
MONGO_IMAGE=mongo:4.0
MONGO_URL=mongodb://mongo/sharelatex

# Redis configuration
REDIS_ENABLED=true
REDIS_IMAGE=redis:5.0
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DATA_PATH=data/redis

# Git bridge configuration
GIT_BRIDGE_ENABLED=false
GIT_BRIDGE_DATA_PATH=data/git-bridge
GIT_BRIDGE_LOG_LEVEL=INFO

# TLS proxy configuration
NGINX_ENABLED=false
NGINX_IMAGE=nginx:1.19-alpine
NGINX_CONFIG_PATH=config/nginx/nginx.conf
NGINX_HTTP_PORT=80
TLS_PORT=443
