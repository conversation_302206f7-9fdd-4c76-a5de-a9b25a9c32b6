// Test the AI detection logic
const content = "@ai";
const aiTriggers = [
  '@ai',
  '@assistant',
  'explain this',
  'what does this mean',
  'help me understand',
  'can you explain',
  'what is this about',
  'summarize this',
  'tell me about'
];

const lowerContent = content.toLowerCase();
const isAiQuery = aiTriggers.some(trigger => lowerContent.includes(trigger));
console.log('Should detect as AI query:', isAiQuery); // Should be true