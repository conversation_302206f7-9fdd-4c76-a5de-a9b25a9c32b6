{"name": "@overleaf/logger", "homepage": "www.overleaf.com", "description": "A centralised logging system for Overleaf", "repository": {"type": "git", "url": "https://github.com/overleaf/overleaf"}, "main": "index.js", "license": "AGPL-3.0-only", "version": "3.1.1", "scripts": {"test": "npm run lint && npm run format && npm run types:check && npm run test:unit", "format": "prettier --list-different $PWD/'**/*.{js,cjs,ts}'", "format:fix": "prettier --write $PWD/'**/*.{js,cjs,ts}'", "lint": "eslint --ext .js --ext .cjs --ext .ts --max-warnings 0 --format unix .", "lint:fix": "eslint --fix --ext .js --ext .cjs --ext .ts .", "test:ci": "npm run test:unit", "test:unit": "mocha --exit test/**/*.{js,cjs}", "types:check": "tsc --noEmit"}, "dependencies": {"@google-cloud/logging-bunyan": "^5.1.0", "@overleaf/fetch-utils": "*", "@overleaf/o-error": "*", "bunyan": "^1.8.14"}, "devDependencies": {"chai": "^4.3.6", "mocha": "^11.1.0", "sandboxed-module": "^2.0.4", "sinon": "^9.2.4", "sinon-chai": "^3.7.0", "typescript": "^5.0.4"}, "peerDependencies": {"@overleaf/metrics": "*"}}