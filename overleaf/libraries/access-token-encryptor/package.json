{"name": "@overleaf/access-token-encryptor", "version": "3.0.0", "description": "", "main": "index.js", "scripts": {"test": "npm run lint && npm run format && npm run types:check && npm run test:unit", "lint": "eslint --ext .js --ext .cjs --ext .ts --max-warnings 0 --format unix .", "lint:fix": "eslint --fix --ext .js --ext .cjs --ext .ts .", "format": "prettier --list-different $PWD/'**/*.{js,cjs,ts}'", "format:fix": "prettier --write $PWD/'**/*.{js,cjs,ts}'", "test:ci": "npm run test:unit", "test:unit": "mocha --exit test/**/*.{js,cjs}", "types:check": "tsc --noEmit"}, "author": "", "license": "AGPL-3.0-only", "dependencies": {"lodash": "^4.17.21"}, "devDependencies": {"chai": "^4.3.6", "chai-as-promised": "^7.1.1", "mocha": "^11.1.0", "sandboxed-module": "^2.0.4", "typescript": "^5.0.4"}}