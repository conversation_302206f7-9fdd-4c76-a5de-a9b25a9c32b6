services:
  clsi:
    command: ["node", "--watch", "app.js"]
    environment:
      - NODE_OPTIONS=--inspect=0.0.0.0:9229
    ports:
      - "127.0.0.1:9230:9229"
    volumes:
      - ../services/clsi/app:/overleaf/services/clsi/app
      - ../services/clsi/app.js:/overleaf/services/clsi/app.js
      - ../services/clsi/config:/overleaf/services/clsi/config

  chat:
    command: ["node", "--watch", "app.js"]
    environment:
      - NODE_OPTIONS=--inspect=0.0.0.0:9229
    ports:
      - "127.0.0.1:9231:9229"
    volumes:
      - ../services/chat/app:/overleaf/services/chat/app
      - ../services/chat/app.js:/overleaf/services/chat/app.js
      - ../services/chat/config:/overleaf/services/chat/config

  contacts:
    command: ["node", "--watch", "app.js"]
    environment:
      - NODE_OPTIONS=--inspect=0.0.0.0:9229
    ports:
      - "127.0.0.1:9232:9229"
    volumes:
      - ../services/contacts/app:/overleaf/services/contacts/app
      - ../services/contacts/app.js:/overleaf/services/contacts/app.js
      - ../services/contacts/config:/overleaf/services/contacts/config

  docstore:
    command: ["node", "--watch", "app.js"]
    environment:
      - NODE_OPTIONS=--inspect=0.0.0.0:9229
    ports:
      - "127.0.0.1:9233:9229"
    volumes:
      - ../services/docstore/app:/overleaf/services/docstore/app
      - ../services/docstore/app.js:/overleaf/services/docstore/app.js
      - ../services/docstore/config:/overleaf/services/docstore/config

  document-updater:
    command: ["node", "--watch", "app.js"]
    environment:
      - NODE_OPTIONS=--inspect=0.0.0.0:9229
    ports:
      - "127.0.0.1:9234:9229"
    volumes:
      - ../services/document-updater/app:/overleaf/services/document-updater/app
      - ../services/document-updater/app.js:/overleaf/services/document-updater/app.js
      - ../services/document-updater/config:/overleaf/services/document-updater/config

  filestore:
    command: ["node", "--watch", "app.js"]
    environment:
      - NODE_OPTIONS=--inspect=0.0.0.0:9229
    ports:
      - "127.0.0.1:9235:9229"
    volumes:
      - ../services/filestore/app:/overleaf/services/filestore/app
      - ../services/filestore/app.js:/overleaf/services/filestore/app.js
      - ../services/filestore/config:/overleaf/services/filestore/config

  history-v1:
    command: ["node", "--watch", "app.js"]
    environment:
      - NODE_OPTIONS=--inspect=0.0.0.0:9229
    ports:
      - "127.0.0.1:9239:9229"
    volumes:
      - ../services/history-v1/api:/overleaf/services/history-v1/api
      - ../services/history-v1/app.js:/overleaf/services/history-v1/app.js
      - ../services/history-v1/config:/overleaf/services/history-v1/config
      - ../services/history-v1/storage:/overleaf/services/history-v1/storage
      - ../services/history-v1/knexfile.js:/overleaf/services/history-v1/knexfile.js
      - ../services/history-v1/migrations:/overleaf/services/history-v1/migrations

  notifications:
    command: ["node", "--watch", "app.js"]
    environment:
      - NODE_OPTIONS=--inspect=0.0.0.0:9229
    ports:
      - "127.0.0.1:9236:9229"
    volumes:
      - ../services/notifications/app:/overleaf/services/notifications/app
      - ../services/notifications/app.js:/overleaf/services/notifications/app.js
      - ../services/notifications/config:/overleaf/services/notifications/config

  project-history:
    command: ["node", "--watch", "app.js"]
    environment:
      - NODE_OPTIONS=--inspect=0.0.0.0:9229
    ports:
      - "127.0.0.1:9240:9229"
    volumes:
      - ../services/project-history/app:/overleaf/services/project-history/app
      - ../services/project-history/app.js:/overleaf/services/project-history/app.js
      - ../services/project-history/config:/overleaf/services/project-history/config

  real-time:
    command: ["node", "--watch", "app.js"]
    environment:
      - NODE_OPTIONS=--inspect=0.0.0.0:9229
    ports:
      - "127.0.0.1:9237:9229"
    volumes:
      - ../services/real-time/app:/overleaf/services/real-time/app
      - ../services/real-time/app.js:/overleaf/services/real-time/app.js
      - ../services/real-time/config:/overleaf/services/real-time/config

  web:
    command: ["node", "--watch", "app.mjs", "--watch-locales"]
    environment:
      - NODE_OPTIONS=--inspect=0.0.0.0:9229
    ports:
      - "127.0.0.1:9229:9229"
    volumes:
      - ../services/web/app:/overleaf/services/web/app
      - ../services/web/app.mjs:/overleaf/services/web/app.mjs
      - ../services/web/config:/overleaf/services/web/config
      - ../services/web/locales:/overleaf/services/web/locales
      - ../services/web/modules:/overleaf/services/web/modules
      - ../services/web/public:/overleaf/services/web/public

  webpack:
    volumes:
      - ../services/web/app:/overleaf/services/web/app
      - ../services/web/config:/overleaf/services/web/config
      - ../services/web/frontend:/overleaf/services/web/frontend
      - ../services/web/locales:/overleaf/services/web/locales
      - ../services/web/modules:/overleaf/services/web/modules
      - ../services/web/public:/overleaf/services/web/public
      - ../services/web/transform:/overleaf/services/web/transform
      - ../services/web/types:/overleaf/services/web/types
      - ../services/web/webpack-plugins:/overleaf/services/web/webpack-plugins
