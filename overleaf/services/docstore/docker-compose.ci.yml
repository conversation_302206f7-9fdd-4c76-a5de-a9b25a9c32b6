# This file was auto-generated, do not edit it directly.
# Instead run bin/update_build_scripts from
# https://github.com/overleaf/internal/

version: "2.3"

services:
  test_unit:
    image: ci/$PROJECT_NAME:$BRANCH_NAME-$BUILD_NUMBER
    user: node
    command: npm run test:unit:_run
    environment:
      NODE_ENV: test
      NODE_OPTIONS: "--unhandled-rejections=strict"


  test_acceptance:
    build: .
    image: ci/$PROJECT_NAME:$BRANCH_NAME-$BUILD_NUMBER
    environment:
      ELASTIC_SEARCH_DSN: es:9200
      MONGO_HOST: mongo
      POSTGRES_HOST: postgres
      GCS_API_ENDPOINT: http://gcs:9090
      GCS_PROJECT_ID: fake
      STORAGE_EMULATOR_HOST: http://gcs:9090/storage/v1
      MOCHA_GREP: ${MOCHA_GREP}
      NODE_ENV: test
      NODE_OPTIONS: "--unhandled-rejections=strict"
    volumes:
      - ../../bin/shared/wait_for_it:/overleaf/bin/shared/wait_for_it
    depends_on:
      mongo:
        condition: service_started
      gcs:
        condition: service_healthy
    user: node
    entrypoint: /overleaf/bin/shared/wait_for_it mongo:27017 --timeout=0 --
    command: npm run test:acceptance


  tar:
    build: .
    image: ci/$PROJECT_NAME:$BRANCH_NAME-$BUILD_NUMBER
    volumes:
      - ./:/tmp/build/
    command: tar -czf /tmp/build/build.tar.gz --exclude=build.tar.gz --exclude-vcs .
    user: root
  mongo:
    image: mongo:8.0.11
    command: --replSet overleaf
    volumes:
      - ../../bin/shared/mongodb-init-replica-set.js:/docker-entrypoint-initdb.d/mongodb-init-replica-set.js
    environment:
      MONGO_INITDB_DATABASE: sharelatex
    extra_hosts:
      # Required when using the automatic database setup for initializing the
      # replica set. This override is not needed when running the setup after
      # starting up mongo.
      - mongo:127.0.0.1
  gcs:
    image: fsouza/fake-gcs-server:1.45.2
    command: ["--port=9090", "--scheme=http"]
    healthcheck:
      test: wget --quiet --output-document=/dev/null http://localhost:9090/storage/v1/b
      interval: 1s
      retries: 20
