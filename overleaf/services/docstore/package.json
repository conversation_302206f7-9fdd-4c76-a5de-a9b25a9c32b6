{"name": "@overleaf/docstore", "description": "A CRUD API for handling text documents in projects", "private": true, "main": "app.js", "scripts": {"start": "node app.js", "test:acceptance:_run": "mocha --recursive --reporter spec --timeout 15000 --exit $@ test/acceptance/js", "test:acceptance": "npm run test:acceptance:_run -- --grep=$MOCHA_GREP", "test:unit:_run": "mocha --recursive --reporter spec $@ test/unit/js", "test:unit": "npm run test:unit:_run -- --grep=$MOCHA_GREP", "nodemon": "node --watch app.js", "lint": "eslint --max-warnings 0 --format unix .", "format": "prettier --list-different $PWD/'**/*.*js'", "format:fix": "prettier --write $PWD/'**/*.*js'", "lint:fix": "eslint --fix .", "types:check": "tsc --noEmit"}, "dependencies": {"@overleaf/fetch-utils": "*", "@overleaf/logger": "*", "@overleaf/metrics": "*", "@overleaf/o-error": "*", "@overleaf/object-persistor": "*", "@overleaf/promise-utils": "*", "@overleaf/settings": "*", "@overleaf/stream-utils": "^0.1.0", "async": "^3.2.5", "body-parser": "^1.20.3", "bunyan": "^1.8.15", "celebrate": "^15.0.3", "express": "^4.21.2", "lodash": "^4.17.21", "mongodb-legacy": "6.1.3", "p-map": "^4.0.0", "request": "^2.88.2"}, "devDependencies": {"@google-cloud/storage": "^6.10.1", "chai": "^4.3.6", "chai-as-promised": "^7.1.1", "mocha": "^11.1.0", "sandboxed-module": "~2.0.4", "sinon": "~9.0.2", "sinon-chai": "^3.7.0", "typescript": "^5.0.4"}}