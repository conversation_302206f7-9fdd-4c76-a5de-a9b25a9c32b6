# This file was auto-generated, do not edit it directly.
# Instead run bin/update_build_scripts from
# https://github.com/overleaf/internal/

version: "2.3"

services:
  test_unit:
    image: node:22.17.0
    volumes:
      - .:/overleaf/services/docstore
      - ../../node_modules:/overleaf/node_modules
      - ../../libraries:/overleaf/libraries
    working_dir: /overleaf/services/docstore
    environment:
      MOCHA_GREP: ${MOCHA_GREP}
      LOG_LEVEL: ${LOG_LEVEL:-}
      NODE_ENV: test
      NODE_OPTIONS: "--unhandled-rejections=strict"
    command: npm run --silent test:unit
    user: node

  test_acceptance:
    image: node:22.17.0
    volumes:
      - .:/overleaf/services/docstore
      - ../../node_modules:/overleaf/node_modules
      - ../../libraries:/overleaf/libraries
      - ../../bin/shared/wait_for_it:/overleaf/bin/shared/wait_for_it
    working_dir: /overleaf/services/docstore
    environment:
      ELASTIC_SEARCH_DSN: es:9200
      MONGO_HOST: mongo
      POSTGRES_HOST: postgres
      GCS_API_ENDPOINT: http://gcs:9090
      GCS_PROJECT_ID: fake
      STORAGE_EMULATOR_HOST: http://gcs:9090/storage/v1
      MOCHA_GREP: ${MOCHA_GREP}
      LOG_LEVEL: ${LOG_LEVEL:-}
      NODE_ENV: test
      NODE_OPTIONS: "--unhandled-rejections=strict"
    user: node
    depends_on:
      mongo:
        condition: service_started
      gcs:
        condition: service_healthy
    entrypoint: /overleaf/bin/shared/wait_for_it mongo:27017 --timeout=0 --
    command: npm run --silent test:acceptance

  mongo:
    image: mongo:8.0.11
    command: --replSet overleaf
    volumes:
      - ../../bin/shared/mongodb-init-replica-set.js:/docker-entrypoint-initdb.d/mongodb-init-replica-set.js
    environment:
      MONGO_INITDB_DATABASE: sharelatex
    extra_hosts:
      # Required when using the automatic database setup for initializing the
      # replica set. This override is not needed when running the setup after
      # starting up mongo.
      - mongo:127.0.0.1

  gcs:
    image: fsouza/fake-gcs-server:1.45.2
    command: ["--port=9090", "--scheme=http"]
    healthcheck:
      test: wget --quiet --output-document=/dev/null http://localhost:9090/storage/v1/b
      interval: 1s
      retries: 20
