export const packageNames: string[] = [
  'inputenc',
  'graphicx',
  'amsmath',
  'geometry',
  'amssymb',
  'hyperref',
  'babel',
  'color',
  'xcolor',
  'url',
  'natbib',
  'fontenc',
  'fancyhdr',
  'amsfonts',
  'booktabs',
  'amsthm',
  'float',
  'tikz',
  'caption',
  'setspace',
  'multirow',
  'array',
  'multicol',
  'titlesec',
  'enumitem',
  'ifthen',
  'listings',
  'blindtext',
  'subcaption',
  'times',
  'bm',
  'subfigure',
  'algorithm',
  'fontspec',
  'biblatex',
  'tabularx',
  'microtype',
  'etoolbox',
  'parskip',
  'calc',
  'verbatim',
  'mathtools',
  'epsfig',
  'wrapfig',
  'lipsum',
  'cite',
  'textcomp',
  'longtable',
  'textpos',
  'algpseudocode',
  'enumerate',
  'subfig',
  'pdfpages',
  'epstopdf',
  'latexsym',
  'lmodern',
  'pifont',
  'ragged2e',
  'rotating',
  'dcolumn',
  'xltxtra',
  'marvosym',
  'indentfirst',
  'xspace',
  'csquotes',
  'xparse',
  'changepage',
  'soul',
  'xunicode',
  'comment',
  'mathrsfs',
  'tocbibind',
  'lastpage',
  'algorithm2e',
  'pgfplots',
  'lineno',
  'algorithmic',
  'fullpage',
  'mathptmx',
  'todonotes',
  'ulem',
  'tweaklist',
  'moderncvstyleclassic',
  'collection',
  'moderncvcompatibility',
  'gensymb',
  'helvet',
  'siunitx',
  'adjustbox',
  'placeins',
  'colortbl',
  'appendix',
  'makeidx',
  'supertabular',
  'ifpdf',
  'framed',
  'aliascnt',
  'layaureo',
  'authblk',
]
