# Hunspell

The files in this directory are:

* `hunspell.wasm`: [<PERSON><PERSON><PERSON><PERSON>](https://github.com/hunspell/hunspell) compiled to WebAssembly using Emscripten, via the [build.sh](../build.sh) script.
* `hunspell.mjs`: a JavaScript wrapper for the WebAssembly module, generated by Emscripten.
* `hunspell.d.ts`: manually-created types for the exports from the JavaScript module.

Note: To speed up compilation on ARM architecture (e.g. Apple M1), add `-arm64` to the Docker image tag in `Dockerfile`.
