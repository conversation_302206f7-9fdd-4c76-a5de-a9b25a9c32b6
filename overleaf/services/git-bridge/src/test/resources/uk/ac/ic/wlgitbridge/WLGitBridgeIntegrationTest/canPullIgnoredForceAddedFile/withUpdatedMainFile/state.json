[{"project": "000000000000000000000000", "getDoc": {"versionID": 5, "createdAt": "2014-11-30T18:40:58.123Z", "email": "<EMAIL>", "name": "<PERSON>+1"}, "getSavedVers": [{"versionID": 5, "comment": "init", "email": "<EMAIL>", "name": "<PERSON>+1", "createdAt": "2014-11-30T18:47:01.456Z"}], "getForVers": [{"versionID": 5, "srcs": [{"content": "content\nupdated\n", "path": "main.tex"}, {"content": "*.txt", "path": "sub/.gitignore"}, {"content": "1", "path": "sub/one.txt"}], "atts": []}], "push": "success", "postback": {"type": "success", "versionID": 5}}]