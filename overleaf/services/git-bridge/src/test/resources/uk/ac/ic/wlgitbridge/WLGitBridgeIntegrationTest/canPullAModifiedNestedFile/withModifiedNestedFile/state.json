[{"project": "000000000000000000000000", "getDoc": {"versionID": 2, "createdAt": "2014-11-30T18:40:58.123Z", "email": "<EMAIL>", "name": "<PERSON>+1"}, "getSavedVers": [{"versionID": 2, "comment": "i added nested stuff", "email": "<EMAIL>", "name": "<PERSON>+1", "createdAt": "2014-11-30T18:48:01.123Z"}, {"versionID": 1, "comment": "added more info on doc GET and error details", "email": "<EMAIL>", "name": "<PERSON>+1", "createdAt": "2014-11-30T18:47:01.456Z"}], "getForVers": [{"versionID": 2, "srcs": [{"content": "content\nadded more stuff\n", "path": "main.tex"}, {"content": "This text is from another file.", "path": "test.tex"}, {"content": "nest1", "path": "nest1/nest1.tex"}, {"content": "nest2", "path": "nest1/nest2/nest2.tex"}], "atts": [{"url": "http://127.0.0.1:3864/withModifiedNestedFile/000000000000000000000000/overleaf-white-410.png", "path": "overleaf-white-410.png"}]}, {"versionID": 1, "srcs": [{"content": "content\n", "path": "main.tex"}, {"content": "This text is from another file.", "path": "test.tex"}], "atts": [{"url": "http://127.0.0.1:3864/base/000000000000000000000000/overleaf-white-410.png", "path": "overleaf-white-410.png"}]}], "push": "success", "postback": {"type": "success", "versionID": 2}}]