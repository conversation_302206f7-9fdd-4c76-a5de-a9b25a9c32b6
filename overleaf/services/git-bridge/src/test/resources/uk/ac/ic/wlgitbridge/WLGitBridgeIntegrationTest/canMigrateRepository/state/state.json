[{"project": "222222222222222222222222", "getDoc": {"versionID": 1, "createdAt": "2014-11-30T18:40:58.123Z", "email": "<EMAIL>", "name": "<PERSON>+1", "migratedFromId": "000000000000000000000000"}, "getSavedVers": [], "getForVers": [{"versionID": 1, "srcs": [{"content": "content\n", "path": "main.tex"}, {"content": "This text is from another file.", "path": "foo/bar/test.tex"}], "atts": [{"url": "http://127.0.0.1:3881/state/000000000000000000000000/min_mean_wait_evm_7_eps_150dpi.png", "path": "min_mean_wait_evm_7_eps_150dpi.png"}]}], "push": "success", "postback": {"type": "success", "versionID": 2}}, {"project": "000000000000000000000000", "getDoc": {"versionID": 1, "createdAt": "2014-11-30T18:40:58.123Z", "email": "<EMAIL>", "name": "<PERSON>+1"}, "getSavedVers": [{"versionID": 1, "comment": "added more info on doc GET and error details", "email": "<EMAIL>", "name": "<PERSON>+1", "createdAt": "2014-11-30T18:47:01.333Z"}], "getForVers": [{"versionID": 1, "srcs": [{"content": "content\n", "path": "main.tex"}, {"content": "This text is from another file.", "path": "foo/bar/test.tex"}], "atts": [{"url": "http://127.0.0.1:3881/state/000000000000000000000000/min_mean_wait_evm_7_eps_150dpi.png", "path": "min_mean_wait_evm_7_eps_150dpi.png"}]}], "push": "success", "postback": {"type": "success", "versionID": 2}}]