[{"project": "000000000000000000000000", "getDoc": {"versionID": 2, "createdAt": "2014-11-30T18:40:58.123Z", "email": "<EMAIL>", "name": "<PERSON>+1"}, "getSavedVers": [{"versionID": 2, "comment": "i deleted the image", "email": "<EMAIL>", "name": "<PERSON>+1", "createdAt": "2014-11-30T18:48:01.123Z"}, {"versionID": 1, "comment": "added more info on doc GET and error details", "email": "<EMAIL>", "name": "<PERSON>+1", "createdAt": "2014-11-30T18:47:01.456Z"}], "getForVers": [{"versionID": 2, "srcs": [{"content": "content\n", "path": "main.tex"}, {"content": "This text is from another file.", "path": "test.tex"}], "atts": [{"url": "http://127.0.0.1:4003/withUpdatedBinaryFiles/000000000000000000000000/overleaf-white-410.png", "path": "overleaf-white-410.png"}, {"url": "http://127.0.0.1:4003/withUpdatedBinaryFiles/000000000000000000000000/overleaf-white-410.png", "path": "overleaf-white-410-copy.png"}]}, {"versionID": 1, "srcs": [{"content": "content\n", "path": "main.tex"}, {"content": "This text is from another file.", "path": "test.tex"}], "atts": [{"url": "http://127.0.0.1:4003/base/000000000000000000000000/overleaf-white-410.png", "path": "overleaf-white-410.png"}, {"url": "http://127.0.0.1:4003/base/000000000000000000000000/overleaf-white-410.png", "path": "overleaf-white-410-copy.png"}]}], "push": "success", "postback": {"type": "success", "versionID": 2}}]