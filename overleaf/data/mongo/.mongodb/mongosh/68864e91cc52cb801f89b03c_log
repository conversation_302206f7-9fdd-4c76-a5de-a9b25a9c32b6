{"t":{"$date":"2025-07-27T16:06:41.696Z"},"s":"E","c":"MONGOSH","id":1000000006,"ctx":"telemetry","msg":"Error: Failed to resolve machine ID","attr":{"stack":"Error: Failed to resolve machine ID\n    at i (eval at module.exports (node:lib-boxednode/mongosh:103:20), <anonymous>:341:199750)\n    at async LoggingAndTelemetry.setupTelemetry (eval at module.exports (node:lib-boxednode/mongosh:103:20), <anonymous>:341:307813)","name":"Error","message":"Failed to resolve machine ID","code":null}}
{"t":{"$date":"2025-07-27T16:06:41.745Z"},"s":"I","c":"MONGOSH","id":1000000005,"ctx":"config","msg":"User updated"}
{"t":{"$date":"2025-07-27T16:06:41.747Z"},"s":"I","c":"MONGOSH","id":1000000048,"ctx":"config","msg":"Loading global configuration file","attr":{"filename":"/etc/mongosh.conf","found":false}}
{"t":{"$date":"2025-07-27T16:06:41.748Z"},"s":"I","c":"MONGOSH","id":1000000000,"ctx":"log","msg":"Starting log","attr":{"execPath":"/usr/bin/mongosh","envInfo":{"EDITOR":null,"NODE_OPTIONS":null,"TERM":null},"version":"2.5.6","distributionKind":"compiled","buildArch":"x64","buildPlatform":"linux","buildTarget":"linux-x64","buildTime":"2025-07-17T14:56:25.712Z","gitVersion":"d31796eee7e35790748e6114858cd55565e8152d","nodeVersion":"v20.19.4","opensslVersion":"3.0.15+quic","sharedOpenssl":false,"runtimeArch":"x64","runtimePlatform":"linux","runtimeGlibcVersion":"2.35","deps":{"nodeDriverVersion":"6.16.0","libmongocryptVersion":"1.13.0","libmongocryptNodeBindingsVersion":"6.3.0","kerberosVersion":"2.1.0"}}}
{"t":{"$date":"2025-07-27T16:06:42.037Z"},"s":"I","c":"DEVTOOLS-CONNECT","id":1000000049,"ctx":"mongosh-connect","msg":"Loaded system CA list","attr":{"caCount":296,"asyncFallbackError":null,"systemCertsError":null,"messages":[]}}
{"t":{"$date":"2025-07-27T16:06:42.071Z"},"s":"I","c":"DEVTOOLS-CONNECT","id":1000000042,"ctx":"mongosh-connect","msg":"Initiating connection attempt","attr":{"uri":"mongodb://localhost:27017/test?directConnection=true&serverSelectionTimeoutMS=2000&appName=mongosh+2.5.6","driver":{"name":"nodejs|mongosh","version":"6.16.0|2.5.6"},"devtoolsConnectVersion":"3.4.1","host":"localhost:27017"}}
{"t":{"$date":"2025-07-27T16:06:42.084Z"},"s":"I","c":"DEVTOOLS-CONNECT","id":1000000035,"ctx":"mongosh-connect","msg":"Server heartbeat succeeded","attr":{"connectionId":"localhost:27017"}}
{"t":{"$date":"2025-07-27T16:06:42.136Z"},"s":"I","c":"DEVTOOLS-CONNECT","id":1000000037,"ctx":"mongosh-connect","msg":"Connection attempt finished"}
{"t":{"$date":"2025-07-27T16:06:42.168Z"},"s":"I","c":"MONGOSH","id":1000000004,"ctx":"connect","msg":"Connecting to server","attr":{"userId":null,"telemetryAnonymousId":"6885ba843948b4e6ac89b03b","connectionUri":"<mongodb uri>","is_localhost":true,"is_do_url":false,"is_atlas_url":false,"is_atlas":false,"server_version":"6.0.25","node_version":"v20.19.4","server_os":"linux","server_arch":"x86_64","is_enterprise":false,"auth_type":null,"is_data_federation":false,"is_stream":false,"dl_version":null,"atlas_version":null,"is_genuine":true,"non_genuine_server_name":"mongodb","is_local_atlas":false,"fcv":"6.0","api_version":null,"api_strict":null,"api_deprecation_errors":null,"atlas_hostname":null}}
{"t":{"$date":"2025-07-27T16:06:42.172Z"},"s":"I","c":"MONGOSH","id":1000000010,"ctx":"shell-api","msg":"Initialized context","attr":{"method":"setCtx","arguments":{}}}
{"t":{"$date":"2025-07-27T16:06:42.174Z"},"s":"I","c":"MONGOSH-SNIPPETS","id":1000000024,"ctx":"snippets","msg":"Fetching snippet index","attr":{"refreshMode":"allow-cached"}}
{"t":{"$date":"2025-07-27T16:06:42.175Z"},"s":"I","c":"MONGOSH-SNIPPETS","id":1000000019,"ctx":"snippets","msg":"Loaded snippets","attr":{"installdir":"/data/db/.mongodb/mongosh/snippets"}}
{"t":{"$date":"2025-07-27T16:06:42.185Z"},"s":"I","c":"MONGOSH-SNIPPETS","id":1000000027,"ctx":"snippets","msg":"Fetching snippet index done"}
{"t":{"$date":"2025-07-27T16:06:42.194Z"},"s":"I","c":"MONGOSH","id":1000000002,"ctx":"repl","msg":"Started REPL","attr":{"version":"2.5.6"}}
{"t":{"$date":"2025-07-27T16:06:42.357Z"},"s":"I","c":"MONGOSH-SNIPPETS","id":1000000024,"ctx":"snippets","msg":"Fetching snippet index","attr":{"refreshMode":"force-refresh"}}
{"t":{"$date":"2025-07-27T16:06:42.374Z"},"s":"I","c":"MONGOSH","id":1000000007,"ctx":"repl","msg":"Evaluating input","attr":{"input":"db.stats().ok"}}
